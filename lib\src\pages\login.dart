// ignore_for_file: unused_import, avoid_print
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'dart:io';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:dio/dio.dart';
import 'package:tencent_cloud_chat_demo/src/provider/wallet_provider.dart';
import 'package:tencent_cloud_chat_demo/utils/init_step.dart';
import 'package:tencent_cloud_chat_demo/utils/user_platform.dart';
import 'package:tencent_cloud_chat_demo/models/login_response.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:tencent_cloud_chat_demo/src/pages/home_page.dart';
import 'package:tencent_cloud_chat_demo/src/pages/privacy/privacy_webview.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_demo/src/routes.dart';
import 'package:tencent_cloud_chat_demo/utils/GenerateTestUserSig.dart';
import 'package:tencent_cloud_chat_demo/utils/commonUtils.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_demo/utils/user_info_local.dart';
import '../../http/dio_instance.dart';
import '../../apis/account_api.dart';
import '../widgets/bottom_sheet_picker.dart';
import '../../models/language_local.dart';
import '../provider/local_setting.dart';
import '../provider/cover_provide.dart';

class LoginUtil {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final GoogleSignIn _googleSignIn = GoogleSignIn();

  /// google登录
  static Future<String?> signInWithGoogle() async {
    final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
    if (googleUser != null) {
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      final AuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      final User? user = (await _auth.signInWithCredential(credential)).user;
      print(user);

      IdTokenResult? idTokenResult = await user?.getIdTokenResult(true);
      return idTokenResult?.token;
    }
    return null;
  }
  /// Facebook登录
  static Future<String?> signInWithFacebook() async {
    try {
      // 请求Facebook登录权限
      final LoginResult result = await FacebookAuth.instance.login(
        permissions: ['email', 'public_profile'],
      );

      if (result.status == LoginStatus.success) {
        // 获取访问令牌
        final AccessToken accessToken = result.accessToken!;

        // 创建Firebase凭据
        final AuthCredential credential = FacebookAuthProvider.credential(
          accessToken.tokenString,
        );

        // 使用Firebase Auth登录
        final User? user = (await _auth.signInWithCredential(credential)).user;
        print(user);

        // 获取ID token
        IdTokenResult? idTokenResult = await user?.getIdTokenResult(true);
        return idTokenResult?.token;
      } else if (result.status == LoginStatus.cancelled) {
        print('Facebook登录被取消');
        return null;
      } else {
        print('Facebook登录失败: ${result.message}');
        return null;
      }
    } catch (e) {
      print('Facebook登录错误: $e');
      return null;
    }
  }

  ///获取当前用户
  static User? currentUser() {
    return _auth.currentUser;
  }

  /// sign out.
  static Future<void> signOut() async {
    await _auth.signOut();
    await _googleSignIn.signOut();
    await FacebookAuth.instance.logOut();
  }
}

class LoginPage extends StatelessWidget {
  final Function? initIMSDK;

  const LoginPage({Key? key, this.initIMSDK}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: Scaffold(
          body: AppLayout(initIMSDK: initIMSDK),
          resizeToAvoidBottomInset: false,
        ));
  }
}

class AppLayout extends StatelessWidget {
  final Function? initIMSDK;

  const AppLayout({Key? key, this.initIMSDK}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        SystemChannels.textInput.invokeMethod('TextInput.hide');
      },
      child: Stack(
        children: [
          const AppLogo(),
          LoginForm(
            initIMSDK: initIMSDK,
          ),
        ],
      ),
    );
  }
}

class AppLogo extends StatelessWidget {
  const AppLogo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double height = MediaQuery.of(context).size.height;
    final theme = Provider.of<DefaultThemeData>(context).theme;
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          child: Image.asset(
            'assets/login_bg.png',
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            fit: BoxFit.cover,
          ),
        ),
      ],
    );
  }
}

class LoginForm extends StatefulWidget {
  final Function? initIMSDK;

  const LoginForm({Key? key, required this.initIMSDK}) : super(key: key);

  @override
  _LoginFormState createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  bool _isPasswordVisible = false; // 控制密码显示/隐藏
  final CoreServicesImpl coreInstance = TIMUIKitCore.getInstance();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  String userID = '';
  late WalletProvider walletProvider;

  @override
  initState() {
    final CoreServicesImpl _coreInstance = TIMUIKitCore.getInstance();
    _coreInstance.setTheme(theme: DefaultThemeData().theme);
    super.initState();
    checkFirstEnter();
    if (widget.initIMSDK != null) {
      widget.initIMSDK!();
    }
    walletProvider = Provider.of<WalletProvider>(context, listen: false);
  }

  TextSpan webViewLink(String title, String url) {
    return TextSpan(
      text: TIM_t(title),
      style: const TextStyle(
        color: Color.fromRGBO(0, 110, 253, 1),
      ),
      recognizer: TapGestureRecognizer()
        ..onTap = () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) =>
                      PrivacyDocument(title: title, url: url)));
        },
    );
  }

  void checkFirstEnter() async {
    Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
    SharedPreferences prefs = await _prefs;
    String? firstTime = prefs.getString("firstTime");
    if (firstTime != null && firstTime == "true") {
      return;
    }
    showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return CupertinoAlertDialog(
          content: Text.rich(
            TextSpan(
                style: const TextStyle(
                    fontSize: 14, color: Colors.black, height: 2.0),
                children: [
                  TextSpan(
                    text: TIM_t(
                        "欢迎使用腾讯云即时通信 IM，为保护您的个人信息安全，我们更新了《隐私政策》，主要完善了收集用户信息的具体内容和目的、增加了第三方SDK使用等方面的内容。"),
                  ),
                  const TextSpan(
                    text: "\n",
                  ),
                  TextSpan(
                    text: TIM_t("请您点击"),
                  ),
                  webViewLink("《用户协议》",
                      'https://web.sdk.qcloud.com/document/Tencent-IM-User-Agreement.html'),
                  TextSpan(
                    text: TIM_t(", "),
                  ),
                  webViewLink("《隐私协议》",
                      'https://privacy.qq.com/document/preview/1cfe904fb7004b8ab1193a55857f7272'),
                  TextSpan(
                    text: TIM_t(", "),
                  ),
                  webViewLink("《信息收集清单》",
                      'https://privacy.qq.com/document/preview/45ba982a1ce6493597a00f8c86b52a1e'),
                  TextSpan(
                    text: TIM_t("和"),
                  ),
                  webViewLink("《信息共享清单》",
                      'https://privacy.qq.com/document/preview/dea84ac4bb88454794928b77126e9246'),
                  TextSpan(
                      text: TIM_t("并仔细阅读，如您同意以上内容，请点击“同意并继续”，开始使用我们的产品与服务！")),
                ]),
            overflow: TextOverflow.clip,
          ),
          actions: [
            CupertinoButton(
              child: Text(TIM_t("同意并继续"),
                  style: const TextStyle(color: Colors.blue, fontSize: 16)),
              onPressed: () {
                prefs.setString("firstTime", "true");
                Navigator.of(context).pop(true);
              },
            ),
            CupertinoButton(
              child: Text(TIM_t("不同意并退出"),
                  style: const TextStyle(color: Colors.grey, fontSize: 16)),
              onPressed: () {
                exit(0);
              },
            ),
          ],
        );
      },
    );
  }

  directToHomePage() {
    debugPrint('跳转到主页');

    // 查询账户是否开通
    walletProvider.queryAccount();
    walletProvider.getBankList(1, 10);

    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const HomePage(),
      ),
      (route) => false,
    );
  }

  // userLogin(int sdkAppId, String userID, String userSig) async {
  //   // 音视频登录
  //   await TUICallKit.instance.login(sdkAppId, userID, userSig);
  //   // 即时通讯登录
  //   var data = await coreInstance.login(
  //     userID: userID,
  //     userSig: userSig,
  //   );
  //   if (data.code != 0) {
  //     debugPrint('即时通讯登录失败: ${data.desc}');
  //     final option1 = data.desc;
  //     ToastUtils.toast(
  //         TIM_t_para("登录失败{{option1}}", "登录失败$option1")(option1: option1));
  //     await UserInfoLocal.clearLoginInfo();
  //     await RedEnvelopeLocal.clearRedEnvelopeType();
  //     return;
  //   }
  //   directToHomePage();
  // }

  jumpToRegister() {
    Navigator.pushNamed(context, '/register');
  }

  // 点击登录
  handleLogin() async {
    debugPrint('点击登录');
    final loginName = _phoneController.text;
    final password = _passwordController.text;
    final loginDevice = UserPlatform.getLoginDevice();
    debugPrint(
        '[Login] 登录信息: 登录名: $loginName, 密码: $password, 设备类型: ${UserPlatform.getLoginDeviceDesc()}($loginDevice)');
    if (loginName.trim() == '') {
      ToastUtils.toast(TIM_t("请输入账号"));
      return;
    }
    if (password.trim() == '') {
      ToastUtils.toast(TIM_t("请输入密码"));
      return;
    }
    ToastUtils.showLoading();
    try {
      Response response =
          await DioInstance.instance().post(path: '/login', data: {
        "loginName": loginName,
        "password": password,
        "loginDevice": UserPlatform.getLoginDevice()
      });
      final loginResponse = LoginResponse.fromJson(response.data);
      if (loginResponse.ok) {
        final appId = loginResponse.data?.appId;
        final userId = loginResponse.data?.userId;
        final userSig = loginResponse.data?.genUserSig;
        if (appId != null && userId != null && userSig != null) {
          await InitStep.userTuiLogin(appId, userId, userSig);
          await UserInfoLocal.saveLoginResponse(loginResponse.data!);
          directToHomePage();
          
        } else {
          ToastUtils.toast('登录成功但缺少必要参数');
        }
      } else {
        ToastUtils.toast(loginResponse.msg ?? '登录失败');
      }
    } finally {
      ToastUtils.hideLoading();
    }
  }

  googleLogin() async {
    var user = LoginUtil.currentUser();
    if (user != null) {
      print(user);
      await LoginUtil.signOut();
    }
    String? token = await LoginUtil.signInWithGoogle();

    debugPrint('google token: $token');
  }

  facebookLogin()async{
    var user = LoginUtil.currentUser();
    if (user != null) {
      print(user);
      await LoginUtil.signOut();
    }
    String? token = await LoginUtil.signInWithFacebook();
    debugPrint('facebook token: $token');


  }

  // 选择语言
  showLanguageSheet(BuildContext context) {
    // 获取 LocalSetting 实例
    final localSetting = Provider.of<LocalSetting>(context, listen: false);
    // 使用组件
    BottomSheetPicker.show(
      context: context,
      title: TIM_t(''),
      options: [
        BottomSheetOption(
          title: TIM_t("简体中文"),
          isSelected: localSetting.language == LanguageEnum.zhHans.value,
          onTap: () async {
            I18nUtils(null, LanguageEnum.zhHans.value);
            localSetting.language = LanguageEnum.zhHans.value;
          },
        ),
        BottomSheetOption(
          title: TIM_t("英文"), 
          isSelected: localSetting.language == LanguageEnum.en.value,
          onTap: () async {
            I18nUtils(null, LanguageEnum.en.value);
            localSetting.language = LanguageEnum.en.value;
          },
        ),
      ],
      cancelText: TIM_t("取消"),
    );
  }
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(750, 1624),
      minTextAdapt: true,
    );
    final LocalSetting localSetting = Provider.of<LocalSetting>(context);


    return Stack(
      children: [
        Positioned(
          child: SafeArea(
            child: Stack(
              children: [
                // 右上角按钮组
                Positioned(
                  left: 16,
                  right: 16,
                  top: 16,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 语言切换按钮
                      Consumer<LocalSetting>(
                        builder: (context, localSetting, child) {
                          return TextButton(
                            onPressed: () {
                              showLanguageSheet(context);
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: const Color(0xFF0072FC), // #0072FC
                              textStyle: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            child: Text(
                              localSetting.language == LanguageEnum.zhHans.value
                                ? TIM_t('简体中文')
                                : TIM_t('English')
                            ),
                          );
                        },
                      ),
                      // 账号注册按钮
                      TextButton(
                        onPressed: () {
                          Navigator.pushNamed(context, '/register');
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: const Color(0xFF0072FC), // #0072FC
                          textStyle: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        child: Text(TIM_t('账号注册')),
                      ),
                    ],
                  ),
                ),
                // 主要内容
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 48),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 67), // 32 + 32(标题到顶部的距离)
                      // 设置登录logo
                      Image.asset(
                        'assets/login_logo.png',
                        width: 136,
                        height: 136,
                      ),
                      const SizedBox(height: 91),
                      // 手机号输入框
                      Container(
                          height: 48,
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFFFFF),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Row(children: [
                            Image.asset(
                              'assets/login_phone.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                                child: TextField(
                              controller: _phoneController,
                              keyboardType: TextInputType.phone,
                              decoration: InputDecoration(
                                hintText: TIM_t('请输入手机号'),
                                border: InputBorder.none,
                                hintStyle: const TextStyle(
                                  color: Color(0xFF999999),
                                  fontSize: 14,
                                ),
                              ),
                              style: const TextStyle(
                                color: Color(0xFF333333),
                                fontSize: 14,
                              ),
                            ))
                          ])),
                      const SizedBox(height: 16),
                      // 密码输入框
                      Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中
                          children: [
                            Image.asset(
                              'assets/login_lock.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: TextField(
                                controller: _passwordController,
                                keyboardType: TextInputType.text,
                                obscureText: !_isPasswordVisible,
                                // 控制密码显示/隐藏
                                decoration: InputDecoration(
                                  hintText: TIM_t('请输入密码'),
                                  border: InputBorder.none,
                                  isDense: true,
                                  // 减少内边距
                                  contentPadding:
                                      const EdgeInsets.symmetric(vertical: 12),
                                  // 调整内边距
                                  hintStyle: const TextStyle(
                                    color: Color(0xFF999999),
                                    fontSize: 14,
                                  ),
                                  // 添加显示/隐藏密码的按钮
                                  suffixIcon: IconButton(
                                    icon: Image.asset(
                                      _isPasswordVisible
                                          ? 'assets/login/eye_open.png'
                                          : 'assets/login/eye_close.png',
                                      width: 24,
                                      height: 24,
                                      color: const Color(0xFF999999),
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        _isPasswordVisible =
                                            !_isPasswordVisible;
                                      });
                                    },
                                  ),
                                ),
                                style: const TextStyle(
                                  color: Color(0xFF333333),
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 32),
                      SizedBox(
                        width: double.infinity,
                        height: 40,
                        child: ElevatedButton(
                          onPressed: () {
                            handleLogin();
                          },
                          child: Text(
                            TIM_t("登录"),
                            style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Colors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0072FC),
                            foregroundColor: const Color(0xFFCBDBFF),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Row(
                        children: [
                          Expanded(
                            child: Divider(
                              color: Color(0xFFE9E9E9), // 设置分割线颜色
                              thickness: 1, // 设置分割线的厚度
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            child: Text(
                              '或', // 这里的文字可以是你想显示的内容
                              style: TextStyle(
                                color: Color(0xFF999999), // 设置文字颜色
                                fontSize: 16, // 设置字体大小
                              ),
                            ),
                          ),
                          Expanded(
                            child: Divider(
                              color: Color(0xFFE9E9E9), // 设置分割线颜色
                              thickness: 1, // 设置分割线的厚度
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      SizedBox(
                        width: double.infinity,
                        height: 40,
                        child: ElevatedButton(
                          onPressed: () {
                            // TODO: 实现注册逻辑
                            facebookLogin();
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                'assets/login/facebook_icon.png', // 替换为你的图片路径
                                width: 20, // 设置图片的宽度
                                height: 20, // 设置图片的高度
                              ),
                              const SizedBox(width: 8), // 图片和文字之间的间距
                               Text(
                                TIM_t("使用Facebook登录"),
                                style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black),
                              ),
                            ],
                          ),
                          style: ElevatedButton.styleFrom(
                            elevation: 0,

                            backgroundColor: const Color(0xFFFFFFFF),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: const BorderSide(
                                color: Color(0xFFE9E9E9), // 设置边框颜色
                                width: 1, // 设置边框宽度
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        height: 40,
                        child: ElevatedButton(
                          onPressed: () {
                            // TODO: 实现注册逻辑
                            googleLogin();
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                'assets/login/google_icon.png', // 替换为你的图片路径
                                width: 20, // 设置图片的宽度
                                height: 20, // 设置图片的高度
                              ),
                              const SizedBox(width: 8), // 图片和文字之间的间距
                               Text(
                                TIM_t("使用谷歌账号登录"),
                                style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black),
                              ),
                            ],
                          ),
                          style: ElevatedButton.styleFrom(
                            elevation: 0,
                            backgroundColor: const Color(0xFFFFFFFF),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: const BorderSide(
                                color: Color(0xFFE9E9E9), // 设置边框颜色
                                width: 1, // 设置边框宽度
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
